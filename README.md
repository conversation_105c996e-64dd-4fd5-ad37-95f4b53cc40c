# Anthropic Proxy

## Local Development

```bash
npm install
npm run start
```

## Deployment to Vercel

### Prerequisites

- Vercel CLI installed globally: `npm install -g vercel`
- A Vercel account

### Deployment Steps

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd anthropic-proxy
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Deploy to Vercel**

   ```bash
   npm run deploy
   ```

4. **Login to Vercel**

   - The CLI will redirect you to login to Vercel
   - Follow the authentication flow in your browser

5. **Configure Environment Variables**
   - Go to your Vercel project dashboard
   - Navigate to Settings → Environment Variables
   - Add the required environment variables (see below)
   - Redeploy the project to apply the new environment variables

### Environment Variables

Configure the following environment variables in your Vercel project:

#### Setting up Environment Variables:

1. **Generate JWT_SECRET**:

   ```bash
   # Option 1: Using OpenSSL
   openssl rand -base64 32

   # Option 2: Using Node.js
   node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
   ```

2. **In Vercel Dashboard**:

   - Go to your project → Settings → Environment Variables
   - Add each variable:
     - Name: `ANTHROPIC_API_KEY`, Value: `your_anthropic_api_key_here`
     - Name: `JWT_SECRET`, Value: `your_generated_jwt_secret_here`
     - Name: `LOG_LEVEL`, Value: `info` (or your preferred level)
       - Options: `error`, `warn`, `info`, `debug`

3. **Redeploy** your project to apply the environment variables
