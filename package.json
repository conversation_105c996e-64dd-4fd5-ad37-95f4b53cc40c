{"name": "fiddle-anthropic-proxy", "scripts": {"start": "vercel dev", "deploy": "vercel", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@anthropic-ai/sdk": "^0.52.0", "hono": "^4.7.10", "stoker": "^1.4.2", "zod": "^3.25.30"}, "devDependencies": {"@antfu/eslint-config": "^4.13.2", "@types/node": "^22.15.23", "eslint": "^9.27.0", "eslint-plugin-format": "^1.0.1", "vercel": "^32.4.1", "vitest": "^3.1.4"}}